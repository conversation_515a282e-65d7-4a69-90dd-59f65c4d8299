import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Map, PlotDraw, Util } from 'yth-map';
import markImg from '@/assets/position.png';
import './MapDraw.less';
import { Github } from 'react-color/lib/components/github/Github';
import { Button, Spin, message } from 'antd';
import dicParams from '@/utils/dicParams';
import { queryByParkCode } from '@/service/baseModuleApi';
import { Props, SORN, MapCardExpose } from '../MapCard/components/mapTypes';
import tdt from './images/tdt_img.jpg';

// useDebounce 防抖 Hook
const useDebounce = <T,>(value: T, delay: number = 300): T => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const timeout = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(timeout);
  }, [value, delay]);

  return debouncedValue;
};

const map = new Map();

// 地图绘制
const MapCard = forwardRef<MapCardExpose, Props>((props: Props, ref) => {
  const [initLoading, setInitLoading] = useState<boolean>();

  // 地图初始化
  const layers = [
    {
      name: '天地图影像',
      image: tdt,
      show: true,
      list: [
        // 影像
        {
          url: 'http://t0.tianditu.com/img_c/wmts?tk=c8b337de7f212a5178a3b1aa7ec3856b&service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
          layer: 'img',
          style: 'default',
          format: 'image/png',
          tileMatrixSetID: 'c',
          minimumLevel: 1,
          maximumLevel: 18,
          tilingScheme: 2,
        },
        // 影像注记
        {
          url: 'http://t0.tianditu.com/cia_c/wmts?tk=c8b337de7f212a5178a3b1aa7ec3856b&service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
          layer: 'cia',
          style: 'default',
          format: 'image/png',
          tileMatrixSetID: 'c',
          minimumLevel: 1,
          maximumLevel: 18,
          tilingScheme: 2,
        },
      ],
    },
    {
      name: '天地图(矢量)',
      image: tdt,
      show: false,
      list: [
        // 矢量
        {
          url: 'http://t0.tianditu.com/vec_c/wmts?tk=52fea9a68c4c16c39dd759ab4fce602f&service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
          layer: 'vec',
          style: 'default',
          format: 'image/png',
          tileMatrixSetID: 'c',
          minimumLevel: 1,
          maximumLevel: 18,
          tilingScheme: 2,
        },
        // 矢量注记
        {
          url: 'http://t0.tianditu.com/cva_c/wmts?tk=52fea9a68c4c16c39dd759ab4fce602f&service=wmts&request=GetTile&version=1.0.0&LAYER=cva&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
          layer: 'cva',
          style: 'default',
          format: 'image/png',
          tileMatrixSetID: 'c',
          minimumLevel: 1,
          maximumLevel: 18,
          tilingScheme: 2,
        },
      ],
    },
  ];

  // 点位标记
  const [position, setPosition] = useState<SORN[]>(['', '', '']);
  const colorSelectRef = useRef<any>(''); // 标记当前选中的颜色
  const [colorSelect, setColorSelect] = useState<any>('');
  const [tipLabel, setTipLabel] = useState<string>('请选择颜色并开始绘制'); // 设置提示词
  const polygonListRef = useRef<any>([]); // 记录当前绘制的多边形

  /**
   * @abstract 根据传参绘制区域
   */
  const drawAreaByDefault = () => {
    if (props.areaList && props.areaList !== '') {
      try {
        const areaListObj = JSON.parse(props.areaList);
        polygonListRef.current = areaListObj;
        areaListObj.forEach((item: any) => {
          map.layer.addPolyline({
            positions: item.list,
            fill: true,
            outlineColor: item.color,
            fillColor: item.color,
          });
        });
      } catch {
        // 无操作
      }
    }
  };

  const initMap = async () => {
    setInitLoading(true);
    let initPoint: any = dicParams.mapCenterStr;
    if (props.position && props.position.x) {
      initPoint = props.position;
    } else if (props.areaList && props.areaList !== '') {
      try {
        const areaListObj = JSON.parse(props.areaList);
        if (areaListObj && Array.isArray(areaListObj) && areaListObj.length > 0) {
          initPoint = {
            x: areaListObj[0].list[0],
            y: areaListObj[0].list[1],
            z: areaListObj[0].list[2],
          };
        }
      } catch {
        // console.log(err);
      }
    } else {
      const park = await queryByParkCode();
      const mapCenter = park?.center ? JSON.parse(park?.center) : {};
      initPoint = {
        x: mapCenter?.x ?? 0,
        y: mapCenter?.y ?? 0,
        z: 10,
      };
    }

    map.initMap({
      container: 'map', // 地图承载容器
      sceneModePicker: true, // 二三维选择按钮-显示控制
      sceneModeChose: 3, // 显示模式 三维: 3 二维: 2  默认值: 3
      positionDisplay: true, // 右下角经纬度显示 默认值: true
      compassDisplay: true, // 罗盘显示 默认值: true
      hostAddr: 'http://***************:8096/check',
      components: true,

      // 初始位置,地图重置(replace)时用到
      initPlace: {
        point: initPoint,
      },
      layersPro: layers,
      // 地图重置按钮   默认视角, 罗盘中恢复的视角，默认是中国范围
      defaultView: {
        rect: [112.96100967885242, 28.194319720664925, 112.97098015969033, 28.198415260838136],
      },
      // 完成地图加载
      callback: () => {
        setInitLoading(false);
        drawAreaByDefault();
        map.flyObject(initPoint);
        map.setMapBackground('天地图影像');

        map.plotDraw = new PlotDraw({
          map,
          // callback: (geo: any) => {
          //   console.log(geo, 'geo');
          // },
          editable: false,
        });
      },
    });
  };

  // 获取绘制数据
  const getDrawData = () => {
    return polygonListRef.current;
  };

  useEffect(() => {
    initMap();
  }, []);

  useEffect(() => {
    if (!map) return;
    if (props.position instanceof Array) {
      // console.log(props.position, 'prop.position');
      setPosition(props.position);
    }
  }, [map, props.position]);

  const localSearchResult = (result: any) => {
    const resultsList: any = document.getElementById('results');
    if (result && result.pois && Array.isArray(result.pois)) {
      const locations = result.pois;
      locations.forEach((location: any, index: number) => {
        const listItem = document.createElement('li');
        listItem.textContent = `${index + 1}: ${location.name}`;
        listItem.addEventListener('click', () => {
          // alert(location.lonlat); // Show the inner info
          const nLoc: any = location.lonlat.split(',');
          map.flyObject({ x: nLoc[0], y: nLoc[1], z: 155 });
        });
        resultsList.appendChild(listItem);
      });
    } else {
      message.error('无数据');
    }
  };

  // 根据关键字搜索
  const searchLocation = async () => {
    const resultsList: any = document.getElementById('results');
    resultsList.innerHTML = '';
    const inputElement = document.getElementById('inputSearch') as HTMLInputElement;
    const value = inputElement?.value || '';
    const rUrl = `https://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"${value}","level":"11","mapBound":"102.546150,24.396308,103.157679,25.132221","queryType":"1","count":"10","start":"0"}&type=query&tk=${dicParams.mapKey}`;
    fetch(rUrl, {
      referrerPolicy: 'strict-origin-when-cross-origin',
      body: null,
      method: 'GET',
      mode: 'cors',
      headers: {},
      credentials: 'omit',
    })
      .then((res) => {
        // console.log(res);
        return res.json();
      })
      .then((data) => {
        // console.log(data);
        localSearchResult(data);
      });
  };

  // 清空搜索结果
  const clearSearch = () => {
    const searchInput: any = document.getElementById('inputSearch')!;
    const resultsList: any = document.getElementById('results');
    searchInput.value = ''; // Clear search input
    resultsList.innerHTML = ''; // Clear search results
  };

  // 重置地图
  const resetMap = (posClear = false) => {
    setInitLoading(false);
    if (props.position && !posClear) return;
    setPosition(['', '']);
    map.resetPlace();
  };

  // 卸载地图
  const destroyMap = () => {
    resetMap();
    map.resetPlace();
  };

  // 绘制区域
  const drawArea = () => {
    setTipLabel('正在进行绘制');
    map.plotDraw.activate(1, {
      callback: (geo: any) => {
        // setPosition(geo.geometry.coordinates[0][0]);
        const psList: any = [];
        geo.geometry.coordinates[0].forEach((item: any) => {
          psList.push(item[0]);
          psList.push(item[1]);
          psList.push(Util.getHeight({ x: item[0], y: item[1], map }));
          // psList.push(item[2]);
        });
        const currentPolygons = polygonListRef.current;

        map.layer.addPolyline({
          positions: psList,
          fill: true,
          outlineColor: colorSelectRef.current,
          fillColor: colorSelectRef.current,
        });

        currentPolygons.push({
          list: psList,
          color: colorSelectRef.current,
        });
        polygonListRef.current = currentPolygons;

        map.plotDraw.deactivate();
        setTipLabel('请选择颜色并继续绘制');
      },
    });
  };

  const markPositon = (value: any) => {
    map.layer.clearAll();
    // 绘制
    map.layer.addMarker({
      layerName: 'positionSele',
      point: value,
      img: markImg,
      scale: 0.3,
      offset: { x: 0, y: -15 },
    });
  };

  // 转换位置
  const transferPoint = (value: any) => {
    const positionNew = {
      x: value[0],
      y: value[1],
      z: value[2] ?? 1,
    };
    return positionNew;
  };

  // 颜色画笔更改
  const colorChange = (color: any) => {
    setColorSelect(color.hex);
    colorSelectRef.current = color.hex;
    drawArea();
  };

  useImperativeHandle(ref, () => {
    return {
      resetMap,
      destroyMap,
      getDrawData,
    };
  });

  useEffect(() => {
    if (!position[0] || !position[1]) return;
    // console.log(position, 'position');
    // console.log(transferPoint(position));
    map.flyObject(transferPoint(position));
    markPositon(transferPoint(position));
    if (props.position === position) return;
    props.onCoordChange!({ lng: position[0], lat: position[1] });
  }, [useDebounce(position, 200)]);

  // 取消按钮
  const cancelbotton = () => {
    polygonListRef.current = [];
    map.layer.clearAll();
    setColorSelect('');
  };

  return (
    <Spin spinning={initLoading} size="large" wrapperClassName="spin-wrap">
      <main className="map-container">
        <div
          id="map"
          style={{
            width: props.mapConfig?.width || '100%',
            height: props.mapConfig?.height || '100%',
          }}
        />
        <div className="toolbar">
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div className="info ant-card ant-card-bordered">
              <div className="item">
                <div className="search-bar">
                  <div className="search-bar-input">
                    <input id="inputSearch" className="input-area" placeholder="请输入搜索内容" />
                    <Button type="default" className="map-search-btn" onClick={searchLocation}>
                      搜索
                    </Button>
                    <Button className="map-clear-btn" type="default" onClick={clearSearch}>
                      重置
                    </Button>
                  </div>
                  <div className="search-bar-result">
                    <div id="results" />
                  </div>
                </div>
                {colorSelect && colorSelect !== '' && (
                  <div className="color" style={{ backgroundColor: colorSelect }} />
                )}
                <div style={{ marginLeft: '20px', color: 'red' }}>{tipLabel}</div>
              </div>
            </div>
          </div>
        </div>
        {props.operateType !== 'view' && (
          <div className="color-area">
            <Github
              triangle="hide"
              colors={[
                'rgb(255 0 0 / 60%)',
                'rgb(255 197 61 / 60%)',
                'rgb(0 0 255 / 60%)',
                'rgb(0 255 0 / 60%)',
              ]}
              styles={{ input: { display: 'none' } }}
              onChange={(color: any) => {
                colorChange(color);
              }}
            />
            <Button
              className="reset-button"
              type="default"
              onClick={() => {
                cancelbotton();
              }}
            >
              清空
            </Button>
          </div>
        )}
      </main>
    </Spin>
  );
});

// 设置组件的 displayName 用于调试和开发工具
MapCard.displayName = 'MapCard';

export default MapCard;
