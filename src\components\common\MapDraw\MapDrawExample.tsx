import React, { useRef } from 'react';
import { Button } from 'antd';
import MapDraw from './MapDraw';
import type { MapCardExpose } from '../MapCard/components/mapTypes';

/**
 * MapDraw 组件使用示例
 * 展示如何正确使用 TypeScript 类型和 ref
 */
const MapDrawExample: React.FC = () => {
  // 使用 useRef 创建 ref，指定正确的类型
  const mapDrawRef = useRef<MapCardExpose>(null);

  // 重置地图
  const handleResetMap = () => {
    mapDrawRef.current?.resetMap();
  };

  // 销毁地图
  const handleDestroyMap = () => {
    mapDrawRef.current?.destroyMap();
  };

  // 获取绘制数据
  const handleGetDrawData = () => {
    const drawData = mapDrawRef.current?.getDrawData && mapDrawRef.current?.getDrawData();
    console.log('绘制数据:', drawData);

    // drawData 的类型为：
    // Array<{
    //   list: number[];
    //   color: string;
    // }>

    if (drawData && drawData.length > 0) {
      drawData.forEach((area, index) => {
        console.log(`区域 ${index + 1}:`, {
          坐标点: area.list,
          颜色: area.color,
        });
      });
    }
  };

  return (
    <div style={{ width: '100%', height: '600px' }}>
      <div style={{ marginBottom: '10px' }}>
        <Button onClick={handleResetMap} style={{ marginRight: '8px' }}>
          重置地图
        </Button>
        <Button onClick={handleDestroyMap} style={{ marginRight: '8px' }}>
          销毁地图
        </Button>
        <Button onClick={handleGetDrawData} type="primary">
          获取绘制数据
        </Button>
      </div>

      <MapDraw
        ref={mapDrawRef} // 使用 ref 而不是 onRef
        mapConfig={{
          width: '100%',
          height: '500px',
          satellite: true,
        }}
        operateType="edit" // 'view' | 'edit' | 'add'
        areaList="" // 初始区域数据（JSON 字符串）
        onCoordChange={(coords) => {
          console.log('坐标变化:', coords);
        }}
      />
    </div>
  );
};

export default MapDrawExample;
