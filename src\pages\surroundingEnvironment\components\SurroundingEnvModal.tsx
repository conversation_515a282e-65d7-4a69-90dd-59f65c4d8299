import React, { useState, useEffect } from 'react';
import { YTHForm } from 'yth-ui';
import { message, <PERSON><PERSON>, Spin } from 'antd';
import surroundingEnvApi from '@/service/surroundingEnvApi';
import type {
  ApiResponse,
  SurroundingEnvRecord,
  SurroundingEnvEditParams,
} from '@/service/surroundingEnvApi';
import type { Form } from '@formily/core/esm/models';
import formApi from '@/service/formApi';
import MapDraw from '@/components/common/MapDraw/MapDraw';
import type { MapCardExpose } from '@/components/common/MapCard/components/mapTypes';

type PropsTypes = {
  dataObj?: SurroundingEnvRecord;
  closeModal: () => void;
  modalType: 'view' | 'edit' | 'add';
};

/**
 * @description 园区周边环境详情弹窗
 * @returns
 */
const SurroundingEnvModal: React.FC<PropsTypes> = ({
  dataObj,
  closeModal = () => {},
  modalType,
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const form: Form = React.useMemo(() => YTHForm.createForm({}), []);
  const MapCardMethodRef: React.MutableRefObject<MapCardExpose | null> = React.useRef(null);
  const [mapData, setMapData] = useState<string>(''); // 标记区域范围

  // 是否为只读模式
  const isReadOnly: boolean = modalType === 'view';

  useEffect(() => {
    if (modalType === 'add') {
      // 新增模式，设置默认值
      form.setValues({
        areaName: '',
        category: '',
        protectionLevel: '',
        locationDescription: '',
        regionalDescription: '',
      });
    } else if (dataObj) {
      // 查看或编辑模式，设置现有数据
      form.setValues({
        ...dataObj,
      });
    }
  }, [dataObj, modalType, form]);

  // 获取所有的线路进行上传
  const getAllRouter: (value: Array<{ list: number[]; color: string }>) => {
    regionalColor: string;
    regionalDescription: string;
  } = (value) => {
    let colorArr: string = '';
    let routerList: string = '';
    // 遍历获取颜色和坐标数据
    value.forEach((item) => {
      colorArr += `${item.color}&`;
      routerList += `${JSON.stringify(item.list)}&`;
    });
    return {
      regionalColor: colorArr.substring(0, colorArr.length - 1),
      regionalDescription: routerList.substring(0, routerList.length - 1),
    };
  };

  // 保存数据
  const handleSave: () => Promise<void> = async (): Promise<void> => {
    try {
      // 验证表单
      await form.validate();
      setIsLoading(true);
      const drawData: Array<{ list: number[]; color: string }> =
        MapCardMethodRef.current?.getDrawData();

      const params: SurroundingEnvEditParams = {
        areaName: form.values?.areaName,
        category: form.values?.category,
        protectionLevel: form.values?.protectionLevel,
        locationDescription: form.values?.locationDescription,
        ...getAllRouter(drawData),
      };

      let res: ApiResponse<SurroundingEnvRecord>;
      if (modalType === 'add') {
        res = await surroundingEnvApi.insertSurroundingEnv(params);
      } else {
        params.id = dataObj?.id;
        res = await surroundingEnvApi.updateSurroundingEnv(params);
      }

      if (res && res.code === 200) {
        message.success(modalType === 'add' ? '新增成功' : '更新成功');
        closeModal();
      }
    } finally {
      setIsLoading(false);
    }
  };

  // 取消操作
  const handleCancel: () => void = () => {
    form.reset();
    closeModal();
  };

  return (
    <div>
      <Spin spinning={isLoading}>
        {/* 基本信息表单 */}
        <YTHForm form={form} col={2}>
          <YTHForm.Item
            name="areaName"
            title="敏感区域名称"
            labelType={1}
            componentName="Input"
            componentProps={{
              disabled: isReadOnly,
              placeholder: '请输入',
            }}
            required={!isReadOnly}
          />
          <YTHForm.Item
            name="category"
            title="类别"
            labelType={1}
            componentName="Selector"
            componentProps={{
              disabled: isReadOnly,
              request: async () => {
                const { list } = await formApi.getDictionary({
                  condition: {
                    fatherCode: 'A22A08', // 环境类型字典
                  },
                  currentPage: 0,
                  pageSize: 0,
                });
                return list;
              },
              p_props: {
                placeholder: '请选择',
              },
            }}
            required={!isReadOnly}
          />
          <YTHForm.Item
            name="protectionLevel"
            title="保护等级"
            labelType={1}
            componentName="Input"
            componentProps={{
              disabled: isReadOnly,
              placeholder: '请输入保护等级',
            }}
          />
          <YTHForm.Item
            name="locationDescription"
            title="地理位置描述"
            labelType={1}
            componentName="Input"
            componentProps={{
              disabled: isReadOnly,
              placeholder: '请输入地理位置描述',
              rows: 3,
            }}
          />
          {/* <YTHForm.Item
            name="regionalDescription"
            title="区域描绘"
            labelType={1}
            componentName="TextArea"
            componentProps={{
              disabled: isReadOnly,
              placeholder: '请输入区域描绘',
              rows: 3,
            }}
          /> */}
          {modalType === 'view' && (
            <>
              <YTHForm.Item
                name="updateBy"
                title="更新人"
                labelType={1}
                componentName="Input"
                componentProps={{
                  disabled: true,
                }}
              />
              <YTHForm.Item
                name="updateTime"
                title="更新时间"
                labelType={1}
                componentName="Input"
                componentProps={{
                  disabled: true,
                }}
              />
            </>
          )}
        </YTHForm>

        <div style={{ marginTop: 10 }}>
          <span style={{ marginBottom: 10, fontSize: 12 }}>区域描绘</span>
          <MapDraw
            ref={MapCardMethodRef}
            mapConfig={{ satellite: true }}
            operateType={modalType}
            areaList={mapData}
          />
        </div>

        {/* 底部按钮 */}
        <div style={{ textAlign: 'right', marginTop: '20px' }}>
          <Button onClick={handleCancel} style={{ marginRight: '8px' }}>
            取消
          </Button>
          {!isReadOnly && (
            <Button type="primary" onClick={handleSave}>
              {modalType === 'add' ? '新增' : '保存'}
            </Button>
          )}
        </div>
      </Spin>
    </div>
  );
};

export default SurroundingEnvModal;
